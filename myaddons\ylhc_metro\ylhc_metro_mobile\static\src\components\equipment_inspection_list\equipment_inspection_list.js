/** @odoo-module **/

import { Component, useState, onWillUpdateProps, onMounted, onWillUnmount } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";
import { FilterPersistenceMixin } from "../utils/filter_persistence";

export class EquipmentInspectionList extends Component {
    static template = "equipment_inspection_list.EquipmentInspectionList";
    static props = {
        ids: { type: String, optional: true },
        from: { type: String, optional: true },
        mode: { type: String, optional: true },
    };

    setup() {
        this.router = useService("mobile_router");
        this.notification = useService("notification");
        this.orm = useService("orm");

        // 应用筛选持久化 Mixin
        Object.assign(this, FilterPersistenceMixin);

        // 定义筛选字段
        const filterFields = [
            'filter_date', 'filter_user', 'filter_station', 'filter_status',
            'filter_major', 'filter_line', 'filter_type',
            'filter_user_name', 'userSearchQuery'
        ];
        
        // 保存为实例属性
        this.filterFields = filterFields;

        // 检查是否从详情页返回或从主页来
        const fromDetail = this.props.from === 'equipment_inspection_detail_view';
        const fromMain = this.props.from === 'equipment_inspection';
        console.log('EquipmentInspectionList setup - props:', this.props);
        console.log('从详情页返回:', fromDetail);
        console.log('从主页来:', fromMain);
        
        // 初始化筛选持久化
        const savedFilters = this.initFilterPersistence('equipment_inspection_list', filterFields, this.props);
        
        // 如果从主页来带有筛选参数，应用筛选参数
        if (fromMain) {
            if (this.props.filter_date) {
                savedFilters.filter_date = this.props.filter_date;
                savedFilters.isFilterExpanded = false;  // 保持折叠状态
            }
            if (this.props.filter_problem === "true") {
                // 需要特殊处理发现问题的筛选
                savedFilters.filter_problem = true;
                savedFilters.isFilterExpanded = false;  // 保持折叠状态
            }
            if (this.props.filter_abnormal === "true") {
                // 需要特殊处理异常的筛选
                savedFilters.filter_abnormal = true;
                savedFilters.isFilterExpanded = false;  // 保持折叠状态
            }
        } else if (!fromDetail) {
            console.log('清空筛选条件');
            this.clearFilterConditions();
            // 重置所有筛选字段
            filterFields.forEach(field => {
                savedFilters[field] = "";
            });
            savedFilters.isFilterExpanded = false;
        } else {
            console.log('保持筛选条件:', savedFilters);
        }

        // 响应 mode 参数
        this.setModeFromProps(this.props);
        onWillUpdateProps((nextProps) => {
            const oldMode = this.mode;
            this.setModeFromProps(nextProps);
            // 如果mode改变了，需要重新加载数据
            if (oldMode !== this.mode) {
                this.loadInspections(null, true);
            }
        });

        this.state = useState({
            inspectionData: [],
            dateStr: "",
            multiSelect: false,
            selectedItems: [],
            exportedImageUrl: null,
            // 分页和加载相关
            loading: false,
            hasMore: true,
            page: 1,
            pageSize: 20,
            // 筛选相关 - 从保存的条件恢复
            isFilterExpanded: false,
            userOptions: [],
            stationOptions: [],
            locationOptions: [],
            majorOptions: [],
            lineOptions: [],
            typeOptions: [],
            // 用户搜索相关
            showUserSelect: false,
            filteredUserOptions: [],
            userLoading: false,
            inspectorLoading: false,
            // 应用保存的筛选条件（不要覆盖）
            ...savedFilters,
            filter_problem: savedFilters.filter_problem || false,
            filter_abnormal: savedFilters.filter_abnormal || false,
        });

        // 只绑定在箭头函数中使用的方法
        this.onUserSearchInput = this.onUserSearchInput.bind(this);
        this.openUserSelect = this.openUserSelect.bind(this);
        this.selectUser = this.selectUser.bind(this);
        this.handleUserInputBlur = this.handleUserInputBlur.bind(this);
        this.toggleSelection = this.toggleSelection.bind(this);
        this.isSelected = this.isSelected.bind(this);
        this.viewDetail = this.viewDetail.bind(this);
        
        // 滚动加载功能
        this._scrollHandler = () => {
            const container = document.querySelector('.equipment-inspection-list-scroll');
            if (!container) {
                console.log('未找到滚动容器 .equipment-inspection-list-scroll');
                return;
            }
            
            const scrollPosition = container.scrollTop + container.clientHeight;
            const scrollHeight = container.scrollHeight;
            const threshold = 50; // 距离底部50px时触发

            if (scrollPosition >= scrollHeight - threshold && 
                this.state.hasMore && 
                !this.state.loading) {
                console.log('触发滚动加载');
                this.loadMoreInspections();
            }
        };

        onMounted(async () => {
            // 先加载选项，再加载数据
            await this.loadOptions();
            
            // 如果从主页来带有筛选参数，直接应用筛选
            if (fromMain && (this.props.filter_date || this.props.filter_problem || this.props.filter_abnormal)) {
                await this.loadData();
            } else {
                await this.loadInspections();
            }
            
            // 添加滚动监听
            const container = document.querySelector('.equipment-inspection-list-scroll');
            if (container) {
                container.addEventListener('scroll', this._scrollHandler);
            }
            
            // 调试：确认方法绑定
            console.log('onFilter 方法是否存在:', typeof this.onFilter);
            console.log('this.onFilter:', this.onFilter);
        });

        onWillUnmount(() => {
            // 移除滚动监听
            const container = document.querySelector('.equipment-inspection-list-scroll');
            if (container) {
                container.removeEventListener('scroll', this._scrollHandler);
            }
        });

        // 格式化日期
        const today = new Date();
        this.state.dateStr = `${today.getFullYear()}年${today.getMonth() + 1}月${today.getDate()}日`;
        // 根据模式更新标题
        this.updateTitle();
    }

    // 设置模式
    setModeFromProps(props) {
        this.mode = props.mode || 'list';
        if (props.ids) {
            this.targetIds = props.ids.split(',').map(id => parseInt(id.trim()));
        }
        console.log('设置模式:', this.mode);
        // 更新标题
        if (this.state) {
            this.updateTitle();
        }
    }
    
    // 更新标题
    updateTitle() {
        const today = new Date();
        const dateStr = `${today.getFullYear()}年${today.getMonth() + 1}月${today.getDate()}日`;
        
        if (this.mode === 'mine') {
            this.state.dateStr = `我的巡检记录 - ${dateStr}`;
        } else if (this.mode === 'all') {
            this.state.dateStr = `巡检统计 - ${dateStr}`;
        } else {
            this.state.dateStr = `设备巡检 - ${dateStr}`;
        }
    }

    // 加载选项数据
    async loadOptions() {
        try {
            const [lines, stations, majors, types, users] = await Promise.all([
                this.orm.searchRead('metro.line', [], ['id', 'name']),
                this.orm.searchRead('metro.station', [], ['id', 'name']),
                this.orm.searchRead('inspection.major', [], ['id', 'name']),
                this.orm.searchRead('inspection.type', [], ['id', 'name']),
                this.orm.searchRead('hr.employee', [], ['id', 'name'])
            ]);

            this.state.lineOptions = lines.map(line => [line.id, line.name]);
            this.state.stationOptions = stations.map(station => [station.id, station.name]);
            this.state.majorOptions = majors.map(major => [major.id, major.name]);
            this.state.typeOptions = types.map(type => [type.id, type.name]);
            this.state.userOptions = users.map(user => [user.id, user.name]);
        } catch (error) {
            console.error("加载选项数据失败：", error);
            this.notification.add("加载选项数据失败", { type: "danger" });
        }
    }

    // 加载巡检数据
    async loadInspections(domain = null, reset = false) {
        console.log('loadInspections 被调用，参数:', { domain, reset, loading: this.state.loading });

        if (this.state.loading) {
            console.log('已在加载中，跳过');
            return;
        }

        try {
            this.state.loading = true;

            if (reset) {
                this.state.page = 1;
                this.state.hasMore = true;
                this.state.inspectionData = [];
                console.log('重置模式：清空数据，重置分页');
            }

            // 如果没有传入domain，则构建domain
            const searchDomain = domain !== null ? domain : this.buildDomain();
            console.log('最终使用的domain:', searchDomain);

            const offset = (this.state.page - 1) * this.state.pageSize;
            console.log('查询参数:', { offset, limit: this.state.pageSize });

            const records = await this.orm.searchRead(
                'equipment.inspection',
                searchDomain,
                [
                    'id', 'line_id', 'location_id', 'major_id', 'type_id',
                    'inspection_person_ids', 'start_time', 'end_time',
                    'inspection_date', 'state', 'inspection_system_ids'
                ],
                {
                    offset: offset,
                    limit: this.state.pageSize,
                    order: 'inspection_date desc, id desc'
                }
            );

            // 获取所有系统行的ID
            const allSystemLineIds = records.reduce((ids, r) => {
                if (r.inspection_system_ids && r.inspection_system_ids.length > 0) {
                    ids.push(...r.inspection_system_ids);
                }
                return ids;
            }, []);

            // 获取系统行信息
            let systemLinesMap = {};
            if (allSystemLineIds.length > 0) {
                const systemLines = await this.orm.searchRead(
                    'equipment.inspection.system.line',
                    [['id', 'in', allSystemLineIds]],
                    ['id', 'system_id', 'subsystem_ids']
                );

                // 获取所有子系统行的ID
                const allSubsystemIds = systemLines.reduce((ids, sl) => {
                    if (sl.subsystem_ids && sl.subsystem_ids.length > 0) {
                        ids.push(...sl.subsystem_ids);
                    }
                    return ids;
                }, []);

                // 获取子系统信息
                let subsystemsMap = {};
                if (allSubsystemIds.length > 0) {
                    const subsystems = await this.orm.searchRead(
                        'equipment.inspection.subsystem.line',
                        [['id', 'in', allSubsystemIds]],
                        ['id', 'subsystem_id', 'inspection_system_line_id']
                    );

                    // 获取子系统名称
                    const subsystemIds = subsystems.map(s => s.subsystem_id[0]);
                    const subsystemNames = await this.orm.searchRead(
                        'inspection.system.line',
                        [['id', 'in', subsystemIds]],
                        ['id', 'name']
                    );

                    const subsystemNameMap = Object.fromEntries(subsystemNames.map(s => [s.id, s.name]));

                    // 按系统行分组子系统
                    subsystems.forEach(subsystem => {
                        const systemLineId = subsystem.inspection_system_line_id[0];
                        if (!subsystemsMap[systemLineId]) {
                            subsystemsMap[systemLineId] = [];
                        }
                        subsystemsMap[systemLineId].push(subsystemNameMap[subsystem.subsystem_id[0]]);
                    });
                }

                // 构建系统行映射
                systemLines.forEach(sl => {
                    if (!systemLinesMap[sl.id]) {
                        systemLinesMap[sl.id] = [];
                    }
                    systemLinesMap[sl.id] = subsystemsMap[sl.id] || [];
                });
            }

            // 获取巡检人员信息
            const allInspectorIds = records.reduce((ids, r) => {
                if (r.inspection_person_ids && r.inspection_person_ids.length > 0) {
                    ids.push(...r.inspection_person_ids);
                }
                return ids;
            }, []);

            let inspectorMap = {};
            if (allInspectorIds.length > 0) {
                const inspectors = await this.orm.searchRead(
                    "hr.employee",
                    [["id", "in", allInspectorIds]],
                    ["id", "name"]
                );
                inspectorMap = Object.fromEntries(inspectors.map(emp => [emp.id, emp.name]));
            }

            // 格式化数据，按照新的格式要求
            const formattedInspections = records.map(r => {
                // 获取该巡检记录的所有子系统名称
                let allSubsystems = [];
                if (r.inspection_system_ids && r.inspection_system_ids.length > 0) {
                    r.inspection_system_ids.forEach(systemLineId => {
                        if (systemLinesMap[systemLineId]) {
                            allSubsystems.push(...systemLinesMap[systemLineId]);
                        }
                    });
                }

                return {
                    id: r.id,
                    name: `TCMWB-SBXJ-${r.inspection_date.replace(/-/g, '')}${String(r.id).padStart(3, '0')}`,
                    time: this.formatDateTimeWithTimezone(r.start_time),
                    inspection_persons: r.inspection_person_ids && r.inspection_person_ids.length > 0
                        ? r.inspection_person_ids.map(id => inspectorMap[id] || '未知').join('/')
                        : '未指定',
                    major: r.major_id ? r.major_id[1] : '未指定',
                    type: r.type_id ? r.type_id[1] : '未指定',
                    location_description: [
                        r.line_id && r.line_id[1],
                        r.location_id && r.location_id[1],
                        allSubsystems.length > 0 ? allSubsystems.join('/') : '无子系统'
                    ].filter(Boolean).join('-'),
                    // 保留原始数据用于详情跳转
                    line_id: r.line_id,
                    location_id: r.location_id,
                    major_id: r.major_id,
                    type_id: r.type_id,
                    start_time: r.start_time,
                    end_time: r.end_time,
                    inspection_date: r.inspection_date,
                    state: r.state,
                    inspection_system_ids: r.inspection_system_ids
                };
            });

            if (reset) {
                this.state.inspectionData = formattedInspections;
            } else {
                this.state.inspectionData.push(...formattedInspections);
            }

            this.state.hasMore = records.length === this.state.pageSize;
            if (this.state.hasMore) {
                this.state.page += 1;
            }

        } catch (error) {
            console.error("加载巡检数据失败：", error);
            this.notification.add("加载数据失败", { type: "danger" });
        } finally {
            this.state.loading = false;
        }
    }

    // 加载更多数据
    async loadMoreInspections() {
        const domain = this.buildDomain();
        await this.loadInspections(domain, false);
    }

    // 构建查询域
    buildDomain() {
        const domain = [];

        if (this.state.filter_date) {
            domain.push(['inspection_date', '=', this.state.filter_date]);
        }

        if (this.state.filter_line) {
            domain.push(['line_id', '=', parseInt(this.state.filter_line)]);
        }

        if (this.state.filter_station) {
            domain.push(['location_id', '=', parseInt(this.state.filter_station)]);
        }

        if (this.state.filter_major) {
            domain.push(['major_id', '=', parseInt(this.state.filter_major)]);
        }

        if (this.state.filter_type) {
            domain.push(['type_id', '=', parseInt(this.state.filter_type)]);
        }

        if (this.state.filter_status) {
            domain.push(['state', '=', this.state.filter_status]);
        }

        if (this.state.filter_user) {
            domain.push(['inspection_person_ids', 'in', [parseInt(this.state.filter_user)]]);
        }
        
        // 处理发现问题的筛选
        if (this.state.filter_problem) {
            domain.push(['note', '!=', false]);
        }
        
        // 处理异常的筛选
        if (this.state.filter_abnormal) {
            domain.push(['abnormal_items', '>', 0]);
        }

        return domain;
    }


    // 切换筛选展开状态
    toggleFilter() {
        this.state.isFilterExpanded = !this.state.isFilterExpanded;
        this.saveFilterConditions();
    }

    // 线路变化处理
    async onLineChange() {
        if (this.state.filter_line) {
            try {
                const stations = await this.orm.searchRead(
                    'metro.station',
                    [['line_id', '=', parseInt(this.state.filter_line)]],
                    ['id', 'name']
                );
                this.state.stationOptions = stations.map(station => [station.id, station.name]);
            } catch (error) {
                console.error("加载站点数据失败：", error);
            }
        } else {
            // 重新加载所有站点
            const stations = await this.orm.searchRead('metro.station', [], ['id', 'name']);
            this.state.stationOptions = stations.map(station => [station.id, station.name]);
        }
        
        this.state.filter_station = '';
        // 只保存筛选条件，不立即加载数据
        this.saveFilterConditions();
    }

    // 筛选条件变化 - 只保存条件，不立即加载数据
    onFilterChange() {
        this.saveFilterConditions();
    }

    // 实现 loadData 方法供 FilterPersistenceMixin 调用
    async loadData() {
        console.log('loadData 被调用');
        
        // 确保关闭用户选择弹窗
        this.state.showUserSelect = false;
        
        // 构建筛选条件
        const domain = this.buildDomain();
        console.log('构建的domain:', domain);

        // 重新加载数据
        await this.loadInspections(domain, true);
    }

    // 重置筛选条件 - 重写Mixin的方法
    async onResetFilter() {
        console.log('onResetFilter 被调用');
        
        // 确保关闭用户选择弹窗
        this.state.showUserSelect = false;
        
        // 清空所有筛选条件
        this.filterFields.forEach(field => {
            this.state[field] = "";
        });

        // 清空选项数组
        this.state.stationOptions = [];

        // 重新加载所有站点选项
        const stations = await this.orm.searchRead('metro.station', [], ['id', 'name']);
        this.state.stationOptions = stations.map(station => [station.id, station.name]);

        // 重新加载所有用户选项
        const users = await this.orm.searchRead(
            "hr.employee",
            [],
            ["id", "name"]
        );
        this.state.userOptions = users.map(u => [String(u.id), u.name]);
        this.state.filteredUserOptions = this.state.userOptions;

        // 额外清空用户搜索相关状态
        this.state.userSearchQuery = '';
        this.state.showUserSelect = false;
        this.state.filter_user_name = '';

        this.state.page = 1;
        this.state.hasMore = true;

        // 重置时关闭筛选页签
        this.state.isFilterExpanded = false;

        // 清除保存的筛选条件
        this.clearFilterConditions();

        console.log('筛选条件已重置，开始重新加载数据');
        // 重新加载数据（不传入domain参数，让loadInspections自己构建空domain）
        await this.loadInspections(null, true);
    }

    // 用户搜索输入
    onUserSearchInput(ev) {
        const query = ev.target.value;
        this.state.filter_user_search = query;
        this.state.userSearchQuery = query;
        
        if (query.length >= 2) {
            this.state.filteredUserOptions = this.state.userOptions.filter(user => 
                user[1].toLowerCase().includes(query.toLowerCase())
            );
            this.state.showUserSuggestions = true;
        } else {
            this.state.showUserSuggestions = false;
        }
    }

    // 打开用户选择
    openUserSelect() {
        this.state.showUserSelect = true;
        this.state.filteredUserOptions = this.state.userOptions;
    }

    // 选择用户
    selectUser(userId, userName) {
        this.state.filter_user = userId;
        this.state.filter_user_name = userName;
        this.state.userSearchQuery = userName;
        this.state.showUserSelect = false;
        // 只保存筛选条件，不立即加载数据
        this.saveFilterConditions();
    }

    // 用户输入失焦
    handleUserInputBlur() {
        setTimeout(() => {
            this.state.showUserSuggestions = false;
        }, 200);
    }

    /**
     * 查看巡检详情
     * @param {Object} inspection - 巡检项目
     */
    viewDetail(inspection) {
        console.log("查看设备巡检:", inspection);
        
        // 在跳转前保存当前筛选条件
        this.saveFilterConditions();
        
        // 准备传递到详情页的参数，包括原始的筛选来源
        const detailParams = {
            id: inspection.id,
            from: "equipment_inspection_list"  // 标记来源为列表页
        };
        
        // 如果当前列表页是从主页带着筛选条件来的，需要传递这些信息
        if (this.props.from === "equipment_inspection") {
            detailParams.original_from = "equipment_inspection";
            if (this.props.filter_date) {
                detailParams.filter_date = this.props.filter_date;
            }
            if (this.props.filter_problem) {
                detailParams.filter_problem = this.props.filter_problem;
            }
            if (this.props.filter_abnormal) {
                detailParams.filter_abnormal = this.props.filter_abnormal;
            }
        }
        
        // 如果有路由跳转到详情页
        if (this.router) {
            this.router.navigate({
                to: "equipment_inspection_detail_view",
                params: detailParams
            });
        } else {
            this.notification.add("暂时无法查看详情", { type: "warning" });
        }
    }

    // 切换选择状态
    toggleSelection(inspectionId, checked) {
        if (checked) {
            if (!this.state.selectedItems.includes(inspectionId)) {
                this.state.selectedItems.push(inspectionId);
            }
        } else {
            const index = this.state.selectedItems.indexOf(inspectionId);
            if (index > -1) {
                this.state.selectedItems.splice(index, 1);
            }
        }
    }

    // 检查是否已选择
    isSelected(inspectionId) {
        return this.state.selectedItems.includes(inspectionId);
    }


    // 生成图片报告
    async generateReport() {
        if (this.state.selectedItems.length === 0) {
            this.notification.add("请先选择要生成报告的巡检记录", { type: "warning" });
            return;
        }

        try {
            // 跳转到设备巡检专用的图片预览页面
            const idsParam = this.state.selectedItems.join(',');

            console.log('跳转参数:', idsParam);
            console.log('选中的记录IDs:', this.state.selectedItems);

            // 使用专门的设备巡检图片预览路由
            this.router.navigate({
                to: "equipment_inspection_image_preview",
                params: {
                    ids: idsParam
                }
            });
        } catch (error) {
            console.error("跳转到图片生成页面失败：", error);
            this.notification.add("跳转失败", { type: "danger" });
        }
    }

    // 返回
    goBack() {
        this.router.navigate({ to: "equipment_inspection" });
    }

    /**
     * 添加新巡检
     */
    addNewInspection() {
        console.log("添加新设备巡检");
        if (this.router) {
            this.router.navigate({ to: "equipment_inspection_form" });
        } else {
            this.notification.add("暂时无法添加新巡检", { type: "warning" });
        }
    }


    // 格式化日期时间
    formatDateTime(dateTimeStr) {
        if (!dateTimeStr) return '';
        const date = new Date(dateTimeStr);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // 格式化日期时间并处理时区（UTC+8）
    formatDateTimeWithTimezone(dateTimeStr) {
        if (!dateTimeStr) return '';
        const date = new Date(dateTimeStr);
        // 添加8小时（UTC+8）
        date.setHours(date.getHours() + 8);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // 获取状态显示文本
    getStateText(state) {
        const stateMap = {
            'draft': '草稿',
            'in_progress': '进行中',
            'done': '已完成',
            'cancelled': '已取消'
        };
        return stateMap[state] || state;
    }

    // 获取状态样式类
    getStateClass(state) {
        const classMap = {
            'draft': 'state-draft',
            'in_progress': 'state-progress',
            'done': 'state-done',
            'cancelled': 'state-cancelled'
        };
        return classMap[state] || '';
    }

    // 保存筛选条件到本地存储
    saveFilterConditions() {
        const filterConditions = {
            filter_line: this.state.filter_line,
            filter_station: this.state.filter_station,
            filter_major: this.state.filter_major,
            filter_type: this.state.filter_type,
            filter_status: this.state.filter_status,
            filter_date: this.state.filter_date,
            filter_user: this.state.filter_user,
            filter_user_name: this.state.filter_user_name,
            userSearchQuery: this.state.userSearchQuery,
        };
        try {
            localStorage.setItem('equipment_inspection_list_filters', JSON.stringify(filterConditions));
        } catch (error) {
            console.warn('无法保存筛选条件到本地存储:', error);
        }
    }

    // 清除保存的筛选条件
    clearFilterConditions() {
        try {
            localStorage.removeItem('equipment_inspection_list_filters');
        } catch (error) {
            console.warn('无法清除本地存储的筛选条件:', error);
        }
    }

    // 从本地存储恢复筛选条件
    loadFilterConditions() {
        try {
            const saved = localStorage.getItem('equipment_inspection_list_filters');
            if (saved) {
                return JSON.parse(saved);
            }
        } catch (error) {
            console.warn('无法从本地存储加载筛选条件:', error);
        }
        return {};
    }
}
