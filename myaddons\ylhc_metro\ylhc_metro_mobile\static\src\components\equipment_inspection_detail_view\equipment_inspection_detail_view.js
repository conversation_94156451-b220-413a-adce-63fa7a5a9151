/** @odoo-module **/

import { Component, useState, onMounted } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";

export class EquipmentInspectionDetailView extends Component {
    static template = "equipment_inspection_detail_view.EquipmentInspectionDetailView";
    static props = {
        id: { type: String, optional: true },
        fromMode: { type: String, optional: true },
        from: { type: String, optional: true },
        original_from: { type: String, optional: true },
        filter_date: { type: String, optional: true },
        filter_problem: { type: String, optional: true },
        filter_abnormal: { type: String, optional: true },
    };

    setup() {
        this.router = useService("mobile_router");
        this.notification = useService("notification");
        this.orm = useService("orm");

        this.state = useState({
            inspection: null,
            loading: true,
            previewImage: null,
            expandedSystems: new Set()  // 展开的系统ID集合
        });

        onMounted(() => {
            this.loadInspectionDetail();
        });

        // 绑定方法
        this.showImagePreview = this.showImagePreview.bind(this);
        this.closeImagePreview = this.closeImagePreview.bind(this);
        this.toggleSystem = this.toggleSystem.bind(this);
        this.isSystemExpanded = this.isSystemExpanded.bind(this);
        this.getTotalChecked = this.getTotalChecked.bind(this);
        this.getTotalItems = this.getTotalItems.bind(this);
        this.getResultIcon = this.getResultIcon.bind(this);
    }

    // 加载巡检详情
    async loadInspectionDetail() {
        try {
            this.state.loading = true;

            // 获取路由参数 - 优先从 props 获取，如果没有则从路由器服务获取
            let inspectionId = this.props && this.props.id;
            let fromMode = this.props && this.props.fromMode;

            if (!inspectionId) {
                // 尝试从路由器服务获取当前路由参数
                const currentParams = this.router.getCurrentParams();
                inspectionId = currentParams && currentParams.id;
                fromMode = currentParams && currentParams.fromMode;
            }

            // 保存fromMode和from参数用于返回
            this.fromMode = fromMode || "list";
            this.from = this.props && this.props.from;

            if (!inspectionId) {
                this.notification.add("缺少巡检记录ID", { type: "danger" });
                this.goBack();
                return;
            }

            const inspection = await this.orm.read(
                'equipment.inspection',
                [parseInt(inspectionId)],
                [
                    'id', 'name', 'line_id', 'location_id', 'major_id', 'type_id',
                    'inspection_person_ids', 'start_time', 'end_time',
                    'inspection_date', 'state', 'inspection_system_ids'
                ]
            );

            if (!inspection || inspection.length === 0) {
                this.notification.add("巡检记录不存在", { type: "danger" });
                this.goBack();
                return;
            }

            this.state.inspection = inspection[0];

            // 加载巡检人员详情
            await this.loadInspectionPersons();

            // 加载系统详情
            await this.loadSystemDetails();

        } catch (error) {
            console.error("加载巡检详情失败：", error);
            this.notification.add("加载详情失败", { type: "danger" });
            this.goBack();
        } finally {
            this.state.loading = false;
        }
    }

    // 加载巡检人员详情
    async loadInspectionPersons() {
        try {
            if (!this.state.inspection.inspection_person_ids || this.state.inspection.inspection_person_ids.length === 0) {
                console.log("没有巡检人员数据需要加载");
                this.state.inspection.inspection_persons = [];
                return;
            }

            console.log("开始加载巡检人员详情，人员ID:", this.state.inspection.inspection_person_ids);

            // 提取人员ID（处理可能的数组格式）
            let personIds = [];
            this.state.inspection.inspection_person_ids.forEach(item => {
                if (Array.isArray(item)) {
                    // 如果是 [id, name] 格式，取第一个元素
                    personIds.push(item[0]);
                } else {
                    // 如果直接是ID
                    personIds.push(item);
                }
            });

            console.log("提取的人员ID:", personIds);

            if (personIds.length > 0) {
                const persons = await this.orm.read(
                    'hr.employee',
                    personIds,
                    ['id', 'name']
                );

                console.log("巡检人员信息加载成功:", persons);
                this.state.inspection.inspection_persons = persons;
            } else {
                this.state.inspection.inspection_persons = [];
            }

        } catch (error) {
            console.error("加载巡检人员详情失败：", error);
            this.state.inspection.inspection_persons = [];
        }
    }

    // 加载系统详情
    async loadSystemDetails() {
        try {
            if (!this.state.inspection.inspection_system_ids.length) {
                console.log("没有系统数据需要加载");
                return;
            }

            console.log("开始加载系统详情，系统ID:", this.state.inspection.inspection_system_ids);

            const systemRecords = await this.orm.read(
                'equipment.inspection.system.line',
                this.state.inspection.inspection_system_ids,
                ['id', 'system_id', 'subsystem_ids']
            );

            console.log("系统记录加载成功:", systemRecords);

            // 加载子系统详情
            const allSubsystemIds = [];
            systemRecords.forEach(system => {
                allSubsystemIds.push(...system.subsystem_ids);
            });

            let subsystemRecords = [];
            if (allSubsystemIds.length > 0) {
                console.log("开始加载子系统详情，子系统ID:", allSubsystemIds);

                subsystemRecords = await this.orm.read(
                    'equipment.inspection.subsystem.line',
                    allSubsystemIds,
                    [
                        'id', 'subsystem_id', 'result', 'note',
                        'inspection_time', 'inspector_ids', 'attachment_ids'
                    ]
                );

                console.log("子系统记录加载成功:", subsystemRecords);

                // 加载人员信息
                const allInspectorIds = [];
                subsystemRecords.forEach(subsystem => {
                    if (subsystem.inspector_ids && subsystem.inspector_ids.length > 0) {
                        allInspectorIds.push(...subsystem.inspector_ids);
                    }
                });

                let inspectors = [];
                if (allInspectorIds.length > 0) {
                    const uniqueInspectorIds = [...new Set(allInspectorIds)]; // 去重
                    console.log("开始加载人员信息，人员ID:", uniqueInspectorIds);

                    inspectors = await this.orm.read(
                        'hr.employee',
                        uniqueInspectorIds,
                        ['id', 'name']
                    );

                    console.log("人员信息加载成功:", inspectors);
                }

                // 加载附件信息
                const allAttachmentIds = [];
                subsystemRecords.forEach(subsystem => {
                    allAttachmentIds.push(...subsystem.attachment_ids);
                });

                let attachments = [];
                if (allAttachmentIds.length > 0) {
                    attachments = await this.orm.read(
                        'ir.attachment',
                        allAttachmentIds,
                        ['id', 'name', 'mimetype', 'datas', 'res_id']
                    );
                }

                // 将人员和附件关联到子系统
                subsystemRecords.forEach(subsystem => {
                    // 关联人员信息
                    if (subsystem.inspector_ids && subsystem.inspector_ids.length > 0) {
                        subsystem.inspectors = inspectors.filter(emp =>
                            subsystem.inspector_ids.includes(emp.id)
                        );
                    } else {
                        subsystem.inspectors = [];
                    }

                    // 关联附件信息
                    subsystem.attachments = attachments.filter(att =>
                        subsystem.attachment_ids.includes(att.id)
                    );
                });
            }

            // 组织数据结构
            this.state.inspection.systems = systemRecords.map(system => ({
                ...system,
                subsystems: subsystemRecords.filter(sub => 
                    system.subsystem_ids.includes(sub.id)
                )
            }));

            console.log("系统详情加载完成，最终数据结构:", this.state.inspection.systems);

        } catch (error) {
            console.error("加载系统详情失败：", error);
            console.error("错误详情:", error.message);
            console.error("错误堆栈:", error.stack);

            // 检查是否是权限问题
            if (error.message && error.message.includes('404')) {
                this.notification.add("数据不存在或无权限访问", { type: "danger" });
            } else if (error.message && error.message.includes('403')) {
                this.notification.add("无权限访问系统详情", { type: "danger" });
            } else {
                this.notification.add("加载系统详情失败: " + (error.message || "未知错误"), { type: "danger" });
            }
        }
    }

    // 显示图片预览
    showImagePreview(attachment) {
        this.state.previewImage = {
            name: attachment.name,
            url: `/web/content/${attachment.id}?download=true`
        };
    }

    // 关闭图片预览
    closeImagePreview() {
        this.state.previewImage = null;
    }

    // 返回
    goBack() {
        // 如果来自列表页，返回时传递来源参数
        if (this.from === "equipment_inspection_list" || this.fromMode === "list") {
            const returnParams = {
                from: "equipment_inspection_detail_view"  // 标记来源为详情页
            };
            
            // 如果有原始的筛选参数，需要传递回去
            if (this.props.original_from === "equipment_inspection") {
                returnParams.from = "equipment_inspection";  // 保持原始来源
                if (this.props.filter_date) {
                    returnParams.filter_date = this.props.filter_date;
                }
                if (this.props.filter_problem) {
                    returnParams.filter_problem = this.props.filter_problem;
                }
                if (this.props.filter_abnormal) {
                    returnParams.filter_abnormal = this.props.filter_abnormal;
                }
            }
            
            this.router.navigate({ 
                to: "equipment_inspection_list",
                params: returnParams
            });
        } else {
            // 默认返回到设备巡检主页
            this.router.navigate({ to: "equipment_inspection" });
        }
    }

    // 格式化日期时间
    formatDateTime(dateTimeStr) {
        if (!dateTimeStr) return '';
        const date = new Date(dateTimeStr);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // 获取状态显示文本
    getStateText(state) {
        const stateMap = {
            'draft': '草稿',
            'in_progress': '进行中',
            'done': '已完成',
            'cancelled': '已取消'
        };
        return stateMap[state] || state;
    }

    // 获取状态样式类
    getStateClass(state) {
        const classMap = {
            'draft': 'state-draft',
            'in_progress': 'state-progress',
            'done': 'state-done',
            'cancelled': 'state-cancelled'
        };
        return classMap[state] || '';
    }

    // 获取结果显示文本
    getResultText(result) {
        const resultMap = {
            'normal': '正常',
            'abnormal': '异常'
        };
        return resultMap[result] || result;
    }

    // 获取结果样式类
    getResultClass(result) {
        const classMap = {
            'normal': 'result-normal',
            'abnormal': 'result-abnormal'
        };
        return classMap[result] || '';
    }

    // 计算巡检时长
    getInspectionDuration() {
        if (!this.state.inspection.start_time || !this.state.inspection.end_time) {
            return '';
        }
        
        const start = new Date(this.state.inspection.start_time);
        const end = new Date(this.state.inspection.end_time);
        const diffMs = end - start;
        
        const hours = Math.floor(diffMs / (1000 * 60 * 60));
        const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
        
        if (hours > 0) {
            return `${hours}小时${minutes}分钟`;
        } else {
            return `${minutes}分钟`;
        }
    }

    // 获取统计信息
    getStatistics() {
        if (!this.state.inspection.systems) {
            return { total: 0, normal: 0, abnormal: 0, unchecked: 0 };
        }

        let total = 0;
        let normal = 0;
        let abnormal = 0;
        let unchecked = 0;

        this.state.inspection.systems.forEach(system => {
            system.subsystems.forEach(subsystem => {
                total++;
                if (subsystem.result === 'normal') {
                    normal++;
                } else if (subsystem.result === 'abnormal') {
                    abnormal++;
                } else {
                    unchecked++;
                }
            });
        });

        return { total, normal, abnormal, unchecked };
    }

    // 切换系统展开/折叠状态
    toggleSystem(systemId) {
        if (this.state.expandedSystems.has(systemId)) {
            this.state.expandedSystems.delete(systemId);
        } else {
            this.state.expandedSystems.add(systemId);
        }
        // 触发重新渲染
        this.state.expandedSystems = new Set(this.state.expandedSystems);
    }

    // 检查系统是否展开
    isSystemExpanded(systemId) {
        return this.state.expandedSystems.has(systemId);
    }

    // 获取总检查项目数
    getTotalItems() {
        if (!this.state.inspection || !this.state.inspection.systems) {
            return 0;
        }

        let total = 0;
        this.state.inspection.systems.forEach(system => {
            total += system.subsystems.length;
        });
        return total;
    }

    // 获取已检查项目数
    getTotalChecked() {
        if (!this.state.inspection || !this.state.inspection.systems) {
            return 0;
        }

        let checked = 0;
        this.state.inspection.systems.forEach(system => {
            system.subsystems.forEach(subsystem => {
                if (subsystem.result) {
                    checked++;
                }
            });
        });
        return checked;
    }

    // 获取结果图标
    getResultIcon(result) {
        switch (result) {
            case 'normal':
                return 'fa fa-check-circle';
            case 'abnormal':
                return 'fa fa-exclamation-triangle';
            default:
                return 'fa fa-clock-o';
        }
    }
}
