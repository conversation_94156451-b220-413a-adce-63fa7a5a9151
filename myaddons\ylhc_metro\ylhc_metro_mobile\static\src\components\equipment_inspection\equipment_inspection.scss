// 认证检查状态样式
.inspection-checking-auth {
  width: 100vw;
  height: 100vh;
  min-height: 100vh;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .inspection-checking-content {
    text-align: center;
    
    .inspection-checking-spinner {
      margin-bottom: 16px;
      
      i {
        font-size: 32px;
        color: #00A09D;
      }
    }
    
    .inspection-checking-text {
      font-size: 16px;
      color: #666;
      margin: 0;
    }
  }
}

.inspection {
  &-container {
    display: flex;
    flex-direction: column;
    padding: 16px;
    background-color: #f5f7fa;
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 80px; // 底部导航栏空间
  }

  &-header {
    text-align: center;
    background: linear-gradient(135deg, #00A09D 0%, #00bcd4 100%);
    padding: 40px 16px;
    margin: 0;
    border-radius: 0 0 15px 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    color: #fff;
    position: relative;
    
    .back-button {
      position: absolute;
      left: 16px;
      top: 50%;
      transform: translateY(-50%);
      background: none;
      border: none;
      color: #fff;
      font-size: 1.2rem;
      cursor: pointer;
      padding: 8px;
      
      &:hover {
        opacity: 0.8;
      }
    }
    
    span {
      font-size: 20px;
      font-weight: 600;
      margin: 0;
    }
  }

  &-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    margin-top: 10px;
    gap: 12px;
    padding: 0 4px;
  }

  &-stat {
    background-color: #fff;
    border-radius: 0 0 15px 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    flex: 1;
    padding: 16px 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: #f5f5f5;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &:active {
      transform: translateY(0);
    }

    &:first-child {
      margin-left: 0;
    }
    
    &:last-child {
      margin-right: 0;
    }

    &-number {
      font-size: 22px;
      font-weight: bold;
      color: #00A09D;
      margin-bottom: 4px;
    }

    &-label {
      font-size: 13px;
      color: #666;
    }
  }

  &-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    
    @media (max-width: 480px) {
      grid-template-columns: 1fr;
    }
  }

  &-action {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    padding: 16px 0;
    border: none;
    background-color: #00A09D;
    color: #fff;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-align: center;

    &:hover {
      background-color: #008f8c;
    }
    
    &:active {
      background-color: #007e7b;
      transform: translateY(1px);
    }
  }
}

.o_inspection_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40px 16px;
    background: linear-gradient(135deg, #00A09D 0%, #00bcd4 100%);
    color: white;
    border-radius: 0 0 15px 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin: -16px -16px 0 -16px;
    .o_header_content {
        display: flex;
        align-items: center;
        gap: 15px;
        .o_company_name {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
            padding: 0;
        }
        .o_welcome_text {
            font-size: 14px;
            opacity: 0.9;
            margin: 0;
            white-space: nowrap;
        }
    }
    .go-login-btn {
        background: none;
        border: none;
        color: #fff;
        font-size: 12px;
        font-weight: 400;
        border-radius: 0;
        box-shadow: none;
        cursor: pointer;
        position: relative;
        transition: color 0.2s;
        &::after {
            content: "";
            display: block;
            width: 100%;
            height: 2px;
            background: #fff;
            border-radius: 1px;
            position: absolute;
            left: 0;
            bottom: -2px;
            transition: background 0.2s;
        }
        &:hover {
            color: #e0e0e0;
            &::after {
                background: #e0e0e0;
            }
        }
    }
    .o_header_actions {
        display: flex;
        gap: 10px;
        button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: none;
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            i {
                font-size: 18px;
            }
            .o_notification_badge {
                position: absolute;
                top: -5px;
                right: -5px;
                background-color: #ff5722;
                color: white;
                border-radius: 50%;
                width: 20px;
                height: 20px;
                font-size: 11px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
    }
} 