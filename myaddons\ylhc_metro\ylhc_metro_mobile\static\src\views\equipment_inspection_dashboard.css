/* =================== 禁用Element Plus（饿了么UI）动画和过渡效果 =================== */
/* 精确禁用Element Plus组件的transition和animation，但保留ECharts动画 */
.el-card:not(.chart-card),
.el-card:not(.chart-card) *:not(canvas):not(svg):not(.chart-container):not(.chart-container *),
.el-button:not(.chart-container .el-button),
.el-button:not(.chart-container .el-button) *:not(canvas):not(svg),
.el-select:not(.chart-container .el-select),
.el-select:not(.chart-container .el-select) *:not(canvas):not(svg),
.el-input:not(.chart-container .el-input),
.el-input:not(.chart-container .el-input) *:not(canvas):not(svg),
.el-date-picker:not(.chart-container .el-date-picker),
.el-date-picker:not(.chart-container .el-date-picker) *:not(canvas):not(svg),
.el-button-group:not(.chart-container .el-button-group),
.el-button-group:not(.chart-container .el-button-group) *:not(canvas):not(svg),
.el-dropdown,
.el-dropdown *:not(canvas):not(svg),
.el-tooltip,
.el-tooltip *:not(canvas):not(svg),
.el-row,
.el-row *:not(canvas):not(svg):not(.chart-container):not(.chart-container *),
.el-col,
.el-col *:not(canvas):not(svg):not(.chart-container):not(.chart-container *),
.el-form,
.el-form *:not(canvas):not(svg),
.el-form-item,
.el-form-item *:not(canvas):not(svg),
.el-dialog,
.el-dialog *:not(canvas):not(svg),
.el-loading-mask,
.el-loading-mask *:not(canvas):not(svg),
.el-menu,
.el-menu *:not(canvas):not(svg),
.el-submenu,
.el-submenu *:not(canvas):not(svg),
.el-drawer,
.el-drawer *:not(canvas):not(svg),
.el-radio,
.el-radio *:not(canvas):not(svg),
.el-checkbox,
.el-checkbox *:not(canvas):not(svg),
.el-switch,
.el-switch *:not(canvas):not(svg),
.el-slider,
.el-slider *:not(canvas):not(svg),
.el-progress,
.el-progress *:not(canvas):not(svg),
.el-badge,
.el-badge *:not(canvas):not(svg),
.el-tag,
.el-tag *:not(canvas):not(svg),
.el-alert,
.el-alert *:not(canvas):not(svg),
.el-message,
.el-message *:not(canvas):not(svg),
.el-notification,
.el-notification *:not(canvas):not(svg),
.el-popover,
.el-popover *:not(canvas):not(svg),
.el-cascader,
.el-cascader *:not(canvas):not(svg),
.el-color-picker,
.el-color-picker *:not(canvas):not(svg),
.el-transfer,
.el-transfer *:not(canvas):not(svg),
.el-container,
.el-header,
.el-aside,
.el-main,
.el-footer,
.el-scrollbar,
.el-scrollbar *:not(canvas):not(svg),
.el-carousel,
.el-carousel *:not(canvas):not(svg),
.el-collapse,
.el-collapse *:not(canvas):not(svg),
.el-timeline,
.el-timeline *:not(canvas):not(svg),
.el-divider,
.el-divider *:not(canvas):not(svg),
.el-calendar,
.el-calendar *:not(canvas):not(svg),
.el-image,
.el-image *:not(canvas):not(svg),
.el-backtop,
.el-backtop *:not(canvas):not(svg),
.el-page-header,
.el-page-header *:not(canvas):not(svg),
.el-breadcrumb,
.el-breadcrumb *:not(canvas):not(svg),
.el-pagination,
.el-pagination *:not(canvas):not(svg),
.el-avatar,
.el-avatar *:not(canvas):not(svg),
.el-empty,
.el-empty *:not(canvas):not(svg),
.el-descriptions,
.el-descriptions *:not(canvas):not(svg),
.el-result,
.el-result *:not(canvas):not(svg),
.el-skeleton,
.el-skeleton *:not(canvas):not(svg),
.el-spin,
.el-spin *:not(canvas):not(svg),
.el-affix,
.el-affix *:not(canvas):not(svg),
.el-anchor,
.el-anchor *:not(canvas):not(svg),
.el-statistic,
.el-statistic *:not(canvas):not(svg),
.el-tour,
.el-tour *:not(canvas):not(svg),
.el-watermark,
.el-watermark *:not(canvas):not(svg) {
    transition: none !important;
    animation: none !important;
    animation-duration: 0s !important;
    animation-delay: 0s !important;
    transition-duration: 0s !important;
    transition-delay: 0s !important;
    transform-origin: center !important;
}

/* 禁用Element Plus的伪元素动画 */
.el-card::before,
.el-card::after,
.el-button::before,
.el-button::after,
.el-select::before,
.el-select::after,
.el-input::before,
.el-input::after,
.el-date-picker::before,
.el-date-picker::after,
.el-button-group::before,
.el-button-group::after,
.el-dropdown::before,
.el-dropdown::after,
.el-tooltip::before,
.el-tooltip::after,
.el-row::before,
.el-row::after,
.el-col::before,
.el-col::after {
    transition: none !important;
    animation: none !important;
    animation-duration: 0s !important;
    animation-delay: 0s !important;
    transition-duration: 0s !important;
    transition-delay: 0s !important;
}

/* 特别针对可能引起抖动的Element Plus组件 */
.el-card__header,
.el-card__body,
.el-button:hover,
.el-button:focus,
.el-button:active,
.el-select:hover,
.el-select:focus,
.el-input:hover,
.el-input:focus,
.el-date-picker:hover,
.el-date-picker:focus,
.el-button-group .el-button:hover,
.el-button-group .el-button:focus,
.el-button-group .el-button:active {
    transition: none !important;
    animation: none !important;
    transform: none !important;
}

/* 禁用Element Plus的响应式动画 */
.el-col-xs,
.el-col-sm,
.el-col-md,
.el-col-lg,
.el-col-xl,
.el-col-xs-*,
.el-col-sm-*,
.el-col-md-*,
.el-col-lg-*,
.el-col-xl-* {
    transition: none !important;
    animation: none !important;
}

/* 禁用Element Plus的加载动画（保留ECharts的） */
.el-loading-mask .circular {
    animation: none !important;
}

.el-loading-mask .path {
    animation: none !important;
}

/* =================== 确保ECharts动画正常工作 =================== */
/* 明确允许图表容器及其内容的动画和过渡效果 */
.chart-container,
.chart-container *,
.chart-container canvas,
.chart-container svg,
#majorChart,
#majorChart *,
#systemChart,
#systemChart *,
#stationChart,
#stationChart *,
#issueChart,
#issueChart * {
    transition: all 0.3s ease !important;
    animation: unset !important;
    animation-duration: unset !important;
    animation-delay: unset !important;
    transition-duration: unset !important;
    transition-delay: unset !important;
    transform-origin: unset !important;
}

/* 特别确保canvas元素的动画效果 */
canvas,
svg {
    transition: unset !important;
    animation: unset !important;
}

.el-loading-text {
    animation: none !important;
}

/* ECharts容器保持自身动画 - 确保图表动画正常工作 */
.chart-container canvas,
#majorChart canvas,
#systemChart canvas,
#stationChart canvas,
#issueChart canvas {
    animation-play-state: running !important;
}

/* =================== 页面加载动画和过渡效果 */
@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

@keyframes slideInFromTop {
    0% {
        opacity: 0;
        transform: translateY(-30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* =================== 禁用Element Plus动画效果 =================== */
/* 简化的Element Plus动画禁用规则 */
.el-card,
.el-button,
.el-select,
.el-input,
.el-date-picker,
.el-dropdown,
.el-tooltip,
.el-row,
.el-col,
.el-button-group {
    transition: none !important;
    animation: none !important;
}

/* 禁用Element Plus内置动画类 */
.el-fade-in,
.el-fade-in-linear,
.el-zoom-in-center,
.el-zoom-in-top,
.el-zoom-in-bottom {
    transition: none !important;
    animation: none !important;
}

/* 确保图表容器和页面元素立即可见 */
.chart-container {
    opacity: 1 !important;
    visibility: visible !important;
}

/* 确保图表卡片可见 */
.chart-card {
    opacity: 1 !important;
    visibility: visible !important;
}

/* =================== 页面布局基础样式 =================== */
html, body {
    height: 100vh;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    overflow: hidden;
    position: relative;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    height: 100vh;
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
}

/* Element Plus Container 样式 */
.dashboard-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 1000;
    overflow: hidden;
    animation: fadeIn 0.5s ease-out;
}

/* =================== 顶部导航栏样式 =================== */
.dashboard-header {
    flex: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px !important;
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%) !important;
    color: white;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    height: auto !important;
    animation: slideInFromTop 0.6s ease-out;
    position: relative;
    overflow: hidden;
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
    pointer-events: none;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
    z-index: 1;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo {
    font-size: 36px;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
}

.title-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.dashboard-header .title {
    font-size: 32px !important;
    font-weight: bold !important;
    color: white !important;
    margin: 0 !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
}

.dashboard-header .datetime-subtitle {
    font-size: 16px !important;
    color: rgba(255,255,255,0.95) !important;
    font-family: 'Courier New', monospace !important;
    font-weight: 600 !important;
    margin: 0 !important;
    text-shadow: 0 1px 3px rgba(0,0,0,0.3) !important;
    letter-spacing: 1px !important;
    background: rgba(255,255,255,0.1) !important;
    padding: 4px 12px !important;
    border-radius: 12px !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255,255,255,0.2) !important;
}

.header-right {
    display: flex;
    align-items: center;
    z-index: 1;
}

.enter-btn {
    font-size: 16px !important;
    padding: 12px 24px !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 15px rgba(103, 194, 58, 0.3) !important;
    transition: all 0.3s ease !important;
}

.enter-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(103, 194, 58, 0.4) !important;
}

.enter-btn:active {
    transform: translateY(0) !important;
}

.dashboard-main {
    flex: 1;
    padding: 8px !important;
    overflow: hidden;
    background: transparent;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 96px);
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

/* 图表卡片样式 */
.charts-grid {
    height: 100% !important;
    margin: 0 !important;
}

.charts-grid .el-col {
    height: 50% !important;
    margin-bottom: 0 !important;
    padding-bottom: 8px !important;
    display: flex !important;
    flex-direction: column !important;
}

/* =================== 图表卡片样式 =================== */
.charts-grid .chart-card {
    height: 100% !important;
    width: 100% !important;
    opacity: 1 !important;
    visibility: visible !important;
}



.charts-grid .el-card__body {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px !important;
}

.charts-grid .chart-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* =================== 卡片头部和控件样式优化 =================== */

/* 卡片头部布局 */
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
    margin: 0;
    margin-bottom: 8px;
    flex-shrink: 0;
}

.header-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

/* 图表控件容器 */
.chart-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: -40px;
}

/* 按钮组样式优化 */
.chart-controls .el-button-group {
    flex-shrink: 0;
    margin-right: 8px;
    display: inline-flex;
    align-items: center;
}

.chart-controls .el-button-group .el-button {
    margin: 0;
    padding: 10px 20px;
    font-size: 14px;
    line-height: 1.2;
    white-space: nowrap;
    overflow: visible;
    text-overflow: clip;
    min-width: 80px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
}

/* 选择器和日期选择器样式 */
.chart-controls .el-select {
    min-width: 120px;
    max-width: 140px;
}

.chart-controls .el-date-picker {
    min-width: 120px;
    max-width: 140px;
}

/* =================== 统计面板优化 =================== */
.stats-panel {
    flex: 0 0 160px;
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding: 8px 0;
    opacity: 1 !important;
    visibility: visible !important;
    justify-content: space-evenly;
    align-self: stretch;
    overflow: hidden;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 1px;
    padding: 6px 8px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 6px;
    border-left: 3px solid #409EFF;
    transition: all 0.3s ease;
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0);
    min-width: 120px;
    flex: 1;
    justify-content: center;
    align-items: center;
    text-align: center;
    min-height: 0;
    overflow: hidden;
}

.stat-item:hover {
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.stat-label {
    font-size: 11px;
    color: #666;
    font-weight: 500;
    margin: 0;
    line-height: 1.2;
}

.stat-value {
    font-size: 16px;
    font-weight: bold;
    margin: 0;
    font-family: 'Courier New', monospace;
    line-height: 1.1;
}

.total-checks {
    color: #409EFF;
}

.found-issues {
    color: #F56C6C;
}

.closed-issues {
    color: #67C23A;
}

.pending-issues {
    color: #E6A23C;
}



/* 饼图内容包装器 */
.chart-content-wrapper {
    display: flex;
    align-items: stretch;
    flex: 1;
    gap: 12px;
    overflow: hidden;
    min-height: 0;
    max-height: 100%;
    width: 100%;
    box-sizing: border-box;
}

/* 调整饼图容器 */
.chart-content-wrapper .chart-container {
    flex: 1;
    min-height: 200px;
    max-height: 100%;
    align-self: stretch;
    overflow: hidden;
}

/* =================== Element Plus 组件样式优化 =================== */
.el-card {
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

.el-card__header {
    flex-shrink: 0 !important;
    padding: 8px 12px !important;
    border-bottom: 1px solid #ebeef5;
    width: 100% !important;
    box-sizing: border-box !important;
    overflow: visible !important;
}

.el-card__body {
    flex: 1 !important;
    padding: 8px !important;
    display: flex !important;
    flex-direction: column !important;
    min-height: 0 !important;
    overflow: hidden !important;
    width: 100% !important;
    box-sizing: border-box !important;
    transition: none !important;
}

/* 卡片标题样式 */
.chart-title {
    font-size: 16px;
    font-weight: bold;
    color: #333 !important;
    display: flex;
    align-items: center;
    text-align: left;
    justify-content: flex-start;
}

/* 控件样式优化 */
.chart-controls .el-select,
.chart-controls .el-date-picker {
    border-radius: 8px;
}

.chart-controls .el-button {
    border-radius: 8px;
    font-weight: 500;
    padding: 10px 20px;
    font-size: 14px;
    line-height: 1.2;
    min-width: 80px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
}

.chart-controls .el-button--primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.el-row {
    height: 100% !important;
    width: 100% !important;
    box-sizing: border-box !important;
    display: flex !important;
    flex-wrap: wrap !important;
}

.el-col {
    display: flex !important;
    flex-direction: column !important;
    box-sizing: border-box !important;
    padding-left: 8px !important;
    padding-right: 8px !important;
}

/* 图表容器 */
.chart-container {
    width: 100%;
    height: 100%;
    flex: 1;
    min-height: 200px;
    max-height: 100%;
    max-width: 100%;
    overflow: hidden;
    opacity: 1 !important;
    visibility: visible !important;
    box-sizing: border-box;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .chart-controls {
        flex-direction: column;
        align-items: flex-end;
        gap: 6px;
        margin-left: -50px;
    }
    
    .chart-controls .el-select,
    .chart-controls .el-date-picker {
        min-width: 100px;
        max-width: 120px;
    }
}

@media (max-width: 900px) {
    .dashboard-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
        padding: 16px !important;
    }
    
    .header-left, .header-right {
        justify-content: center;
    }
    
    .logo-section {
        gap: 12px;
    }
    
    .dashboard-header .title {
        font-size: 26px !important;
    }
    
    .dashboard-header .datetime-subtitle {
        font-size: 14px !important;
        padding: 3px 10px !important;
    }
    
    .enter-btn {
        font-size: 14px !important;
        padding: 10px 20px !important;
    }
    
    .dashboard-main {
        padding: 6px !important;
    }
    
    .charts-grid .el-col {
        height: 25% !important;
        padding-bottom: 6px !important;
    }
    
    .chart-container {
        min-height: 200px;
    }
    
    .chart-controls {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        margin-left: -30px;
        gap: 6px;
    }
    
    /* 移动端统计面板调整 */
    .chart-content-wrapper {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }
    
    .stats-panel {
        flex: none;
        flex-direction: row;
        justify-content: space-around;
        gap: 8px;
    }
    
    .stat-item {
        flex: 1;
        padding: 6px 8px;
        text-align: center;
    }
    
    .stat-label {
        font-size: 10px;
    }
    
    .stat-value {
        font-size: 14px;
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        padding: 12px !important;
    }
    
    .logo-section {
        gap: 10px;
    }
    
    .logo {
        font-size: 32px !important;
    }
    
    .dashboard-header .title {
        font-size: 22px !important;
    }
    
    .dashboard-header .datetime-subtitle {
        font-size: 12px !important;
        padding: 2px 8px !important;
        letter-spacing: 0.5px !important;
    }
    
    .enter-btn {
        font-size: 12px !important;
        padding: 8px 16px !important;
    }
    
    .dashboard-main {
        padding: 4px !important;
    }
    
    .el-card__header {
        padding: 8px 12px !important;
    }
    
    .el-card__body {
        padding: 12px !important;
    }
    
    .chart-title {
        font-size: 14px;
    }
    
    .chart-container {
        min-height: 180px;
    }
    
    .chart-controls {
        margin-left: -20px;
        gap: 4px;
    }
    
    .chart-controls .el-button {
        padding: 8px 16px;
        font-size: 12px;
        min-width: 70px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
    }
    
    .chart-controls .el-select,
    .chart-controls .el-date-picker {
        min-width: 70px;
        max-width: 90px;
    }
    
    .stats-panel {
        flex: 0 0 140px;
        gap: 4px;
        padding: 4px 0;
    }
    
    .stat-item {
        padding: 4px 8px;
        gap: 1px;
        min-width: 100px;
    }
    
    .stat-label {
        font-size: 10px;
    }
    
    .stat-value {
        font-size: 14px;
    }
}

/* 大屏幕优化 (1920x1080等) */
@media (min-width: 1600px) {
    .chart-container {
        min-height: 220px;
    }
    
    .el-card__body {
        padding: 12px !important;
    }
    
    .chart-content-wrapper {
        gap: 16px;
    }
    
    .stats-panel {
        flex: 0 0 180px;
        padding: 12px 0;
        justify-content: space-evenly;
        gap: 8px;
    }
    
    .stat-item {
        padding: 8px 10px;
        min-width: 140px;
    }
    
    .stat-label {
        font-size: 12px;
    }
    
    .stat-value {
        font-size: 18px;
    }
}

/* 中等屏幕优化 (1200px-1599px) */
@media (min-width: 1200px) and (max-width: 1599px) {
    .stats-panel {
        flex: 0 0 170px;
        padding: 10px 0;
        justify-content: space-evenly;
        gap: 6px;
    }
    
    .stat-item {
        padding: 7px 9px;
    }
    
    .stat-label {
        font-size: 11px;
    }
    
    .stat-value {
        font-size: 16px;
    }
}

/* 普通屏幕优化 (1000px-1199px) */
@media (min-width: 1000px) and (max-width: 1199px) {
    .stats-panel {
        flex: 0 0 150px;
        padding: 6px 0;
        gap: 4px;
    }
    
    .stat-item {
        padding: 5px 7px;
    }
    
    .stat-label {
        font-size: 10px;
    }
    
    .stat-value {
        font-size: 14px;
    }
}

/* =================== 全局加载和动画效果 =================== */
.loading {
    text-align: center;
    padding: 50px;
    font-size: 18px;
    color: #666;
}

/* 返回按钮样式 */
.drill-back-btn {
    position: fixed;
    top: 120px;
    right: 20px;
    z-index: 9999;
    background: #409EFF;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.drill-back-btn:hover {
    background: #337ecc;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

/* =================== 图表容器稳定性优化 =================== */

/* 页面加载状态管理 - 确保图表始终可见 */
body[data-loading="true"] .chart-container,
body:not([data-loading]) .chart-container {
    opacity: 1 !important;
    visibility: visible !important;
    width: 100% !important;
    height: 100% !important;
    max-width: 100% !important;
    overflow: hidden;
}

/* 图表容器基础样式 - 防止抖动 */
#majorChart, #systemChart, #stationChart, #issueChart {
    min-width: 200px !important;
    min-height: 200px !important;
    width: 100% !important;
    height: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;
    box-sizing: border-box !important;
    position: relative !important;
    overflow: hidden !important;
    display: block !important;
    transition: none !important;
    transform: translateZ(0); /* 硬件加速 */
    contain: layout style paint;
    backface-visibility: hidden;
}

/* 饼图容器特殊处理 */
#majorChart {
    width: 100% !important;
    min-width: 200px !important;
}

/* 布局容器稳定性 */
.chart-card .el-card__body,
.chart-content-wrapper,
.charts-grid .el-col {
    width: 100% !important;
    box-sizing: border-box !important;
    will-change: auto;
    backface-visibility: hidden;
}

/* 卡片悬停效果优化 */
.charts-grid .chart-card {
    transition: none !important;
}

.charts-grid .chart-card:hover {
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1), 
                box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
    transform: translateY(-3px) translateZ(0);
    box-shadow: 0 12px 30px rgba(0,0,0,0.12);
}

/* =================== 精确的Element Plus动画禁用规则 =================== */
/* 只禁用特定Element Plus组件的动画和过渡，不影响图表 */
.el-button:not(.chart-container .el-button),
.el-select:not(.chart-container .el-select),
.el-input:not(.chart-container .el-input),
.el-date-picker:not(.chart-container .el-date-picker),
.el-dropdown:not(.chart-container .el-dropdown),
.el-tooltip:not(.chart-container .el-tooltip),
.el-card__header:not(.chart-card .el-card__header),
.el-card__body:not(.chart-card .el-card__body) {
    transition: none !important;
    animation: none !important;
    animation-duration: 0s !important;
    transition-duration: 0s !important;
}

/* 禁用Element Plus组件的悬停效果，但不影响图表容器 */
.el-button:hover:not(.chart-container .el-button),
.el-button:focus:not(.chart-container .el-button),
.el-button:active:not(.chart-container .el-button),
.el-select:hover:not(.chart-container .el-select),
.el-select:focus:not(.chart-container .el-select) {
    transition: none !important;
    animation: none !important;
    transform: none !important;
}

/* 禁用Element Plus的内置动画类 */
.el-fade-in-linear,
.el-fade-in,
.el-zoom-in-center,
.el-zoom-in-top,
.el-zoom-in-bottom,
.el-zoom-in-left,
.el-zoom-in-right,
.el-slide-right,
.el-slide-left,
.el-slide-up,
.el-slide-down {
    transition: none !important;
    animation: none !important;
    animation-duration: 0s !important;
    transition-duration: 0s !important;
}

/* 禁用Element Plus的加载和过渡动画 */
.el-loading-fade,
.el-message-fade,
.el-notification-fade,
.el-alert-fade,
.el-tooltip-fade,
.el-popover-fade,
.el-drawer-fade,
.el-dialog-fade {
    transition: none !important;
    animation: none !important;
}

/* 确保页面不会因为Element Plus组件变化而抖动 */
* {
    will-change: auto !important;
}

/* 禁用浏览器默认的平滑滚动，可能与Element Plus组件冲突 */
html {
    scroll-behavior: auto !important;
}

/* 确保ECharts图表容器稳定，不会因为Element Plus影响而重新布局 */
.chart-container {
    contain: layout style paint !important;
}