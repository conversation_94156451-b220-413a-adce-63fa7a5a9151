/** @odoo-module **/

import { Component, useState, onWillStart, onMounted } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";
import { rpc } from "@web/core/network/rpc";

export class EquipmentInspection extends Component {
    static template = "ylhc_metro_mobile.EquipmentInspection";

    setup() {
        this.router = useService("mobile_router");
        this.orm = useService('orm');
        this.notification = useService("notification");
        
        // 用户信息初始化
        let userName = "";
        const userStr = localStorage.getItem('odoo_equipment_user');
        if (userStr) {
            const userObj = JSON.parse(userStr);
            userName = userObj.name || "";
        }
        const isLoggedIn = !!userName && userName !== "游客";
        
        this.state = useState({
            stats: [
                { number: 0, label: "当日巡检", action: () => this.onStatClick("当日巡检") },
                { number: 0, label: "发现问题", action: () => this.onStatClick("发现问题") },
                { number: 0, label: "巡检异常", action: () => this.onStatClick("巡检异常") }
            ],
            userName,
            isLoggedIn,
            unreadCount: 0,
            checkingAuth: true, // 添加认证检查状态
        });
        
        this.actions = [
            { name: "开始巡检", action: () => this.startInspection() },
            { name: "巡检记录", action: () => this.showRecords() },
            { name: "巡检统计", action: () => this.showStatistics() },
            // { name: "问题提报", action: () => this.reportIssue() }
        ];
        
        onWillStart(async () => {
            // 首先检查登录状态
            const isAuthenticated = await this._checkAuthentication();
            if (!isAuthenticated) {
                return; // 如果未认证，直接返回，已经跳转到登录页
            }
            
            await this._loadUserInfo();
            this.fetchStats();
        });
        
        onMounted(() => {
            // 可扩展：如注册 Service Worker
        });
    }

    /**
     * 检查用户认证状态
     * @private
     */
    async _checkAuthentication() {
        try {
            // 检查本地存储的用户信息
            const userInfo = this._getStoredUserInfo();
            if (!userInfo) {
                console.log('未找到用户信息，跳转到登录页');
                this._redirectToLogin();
                return false;
            }

            // 检查本地用户信息是否过期
            if (this._isUserInfoExpired(userInfo)) {
                console.log('本地用户信息已过期，跳转到登录页');
                this._clearStoredUserInfo();
                this._redirectToLogin();
                return false;
            }

            // 验证服务器端session是否有效
            const isSessionValid = await this._validateSession();
            if (!isSessionValid) {
                console.log('Session无效，跳转到登录页');
                this._clearStoredUserInfo();
                this._redirectToLogin();
                return false;
            }

            // 认证成功，延长过期时间7天
            this._extendUserInfoExpiry();
            this.state.checkingAuth = false;
            return true;
        } catch (error) {
            console.error('认证检查失败:', error);
            this._clearStoredUserInfo();
            this._redirectToLogin();
            return false;
        }
    }

    /**
     * 获取存储的用户信息
     * @private
     */
    _getStoredUserInfo() {
        try {
            const userInfo = localStorage.getItem('odoo_equipment_user');
            return userInfo ? JSON.parse(userInfo) : null;
        } catch (error) {
            console.error('获取存储的用户信息失败:', error);
            return null;
        }
    }

    /**
     * 清除存储的用户信息
     * @private
     */
    _clearStoredUserInfo() {
        localStorage.removeItem('odoo_equipment_user');
    }

    /**
     * 检查用户信息是否过期
     * @private
     */
    _isUserInfoExpired(userInfo) {
        if (!userInfo.expires_at) return true;
        return Date.now() >= userInfo.expires_at;
    }

    /**
     * 验证session是否有效
     * @private
     */
    async _validateSession() {
        try {
            // 使用get_user_info接口来验证session
            const response = await rpc('/ylhc_metro_mobile/get_user_info', {});
            return response && response.code === 200;
        } catch (error) {
            console.error('验证session失败:', error);
            return false;
        }
    }

    /**
     * 跳转到登录页
     * @private
     */
    _redirectToLogin() {
        this.state.checkingAuth = false;
        this.router.navigate({ to: "equipment_inspection_login" });
    }

    /**
     * 加载用户信息
     * @private
     */
    async _loadUserInfo() {
        try {
            const response = await rpc('/ylhc_metro_mobile/get_user_info', {});
            if (response && response.code === 200 && response.data) {
                // 获取现有用户信息
                const existingUserInfo = this._getStoredUserInfo();
                
                // 更新用户信息，但保持现有的时间戳
                const updatedUserInfo = {
                    ...response.data,
                    last_login_time: existingUserInfo?.last_login_time || Date.now(),
                    last_access_time: existingUserInfo?.last_access_time || Date.now(),
                    expires_at: existingUserInfo?.expires_at || (Date.now() + (7 * 24 * 60 * 60 * 1000))
                };
                localStorage.setItem('odoo_equipment_user', JSON.stringify(updatedUserInfo));
                
                // 更新状态
                this.state.userName = response.data.name;
                this.state.isLoggedIn = true;
                
                console.log('用户信息已更新');
            } else {
                console.warn('获取用户信息失败:', response?.message);
                // 如果获取用户信息失败，可能是session过期，跳转到登录页
                this._redirectToLogin();
            }
        } catch (error) {
            console.error('获取用户信息失败:', error);
            // 如果获取用户信息失败，可能是session过期，跳转到登录页
            this._redirectToLogin();
        }
    }

    async user_logout() {
        // 清理本地
        this._clearStoredUserInfo();
        
        // 注销后端
        try {
            await fetch('/web/session/destroy', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
                body: '{}'
            });
        } catch (e) {
            console.warn('注销后端 session 失败', e);
        }
        
        // 跳转
        this.router.navigate({ to: "inspection_login" });
    }

    async fetchStats() {
        try {
            // 始终获取当前用户的统计数据（用于主页显示）
            const stats = await this.orm.call(
                'equipment.inspection',
                'get_inspection_stats',
                [false],  // 设置is_all为false，获取当前用户的统计数据
                {}
            );
            
            // 确保stats是一个有效的对象
            if (stats && typeof stats === 'object') {
                // 使用默认值0，确保即使后端返回undefined也能正常工作
                const dailyCount = parseInt(stats.daily_count) || 0;
                const issueCount = parseInt(stats.issue_count) || 0;
                const abnormalCount = parseInt(stats.abnormal_count) || 0;

                // 确保state.stats数组存在且有足够的元素
                if (this.state.stats && this.state.stats.length >= 3) {
                    this.state.stats[0].number = dailyCount;
                    this.state.stats[1].number = issueCount;
                    this.state.stats[2].number = abnormalCount;
                }
                console.log('设备巡检统计数据已更新:', { dailyCount, issueCount, abnormalCount });
            }
        } catch (error) {
            console.error('获取设备巡检统计数据失败:', error);
            // 发生错误时重置为0
            if (this.state.stats && this.state.stats.length >= 3) {
                this.state.stats[0].number = 0;
                this.state.stats[1].number = 0;
                this.state.stats[2].number = 0;
            }
        }
    }
    
    startInspection() {
        if (this.router) {
            this.router.navigate({ to: "/equipment_inspection_form" });
        }
    }
    
    showRecords() {
        // 巡检记录 - 只看自己的
        if (this.router) {
            this.router.navigate({
                to: "equipment_inspection_list",
                params: { mode: "mine", from: "equipment_inspection" }
            });
        }
    }
    
    /**
     * 统计数据点击事件
     * @param {string} label - 统计项标签
     */
    onStatClick(label) {
        console.log("点击统计项:", label);
        
        // 根据不同的统计项跳转到列表页并带上筛选参数
        const today = new Date();
        const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
        
        let filterParams = {};
        
        switch(label) {
            case "当日巡检":
                // 筛选当日的巡检记录
                filterParams = {
                    filter_date: todayStr,
                    from: "equipment_inspection"
                };
                break;
                
            case "发现问题":
                // 筛选有备注的记录（表示发现问题）
                filterParams = {
                    filter_problem: "true",
                    from: "equipment_inspection"
                };
                break;
                
            case "巡检异常":
                // 筛选异常的记录
                filterParams = {
                    filter_abnormal: "true",
                    from: "equipment_inspection"
                };
                break;
                
            default:
                console.log("未知的统计项:", label);
                return;
        }
        
        // 跳转到设备巡检列表页，并传递筛选参数
        if (this.router) {
            this.router.navigate({
                to: "equipment_inspection_list",
                params: filterParams
            });
        }
    }

    showStatistics() {
        // 巡检统计 - 看所有的
        if (this.router) {
            this.router.navigate({
                to: "equipment_inspection_list",
                params: { mode: "all", from: "equipment_inspection" }
            });
        }
    }

    reportIssue() {
        if (this.router) {
            this.router.navigate({ to: "/inspection_issue" });
        }
    }

    goBack() {
        if (this.router) {
            this.router.navigate({ to: "home" });
        }
    }

    /**
     * 延长用户信息过期时间
     * @private
     */
    _extendUserInfoExpiry() {
        try {
            const userInfo = this._getStoredUserInfo();
            if (userInfo) {
                const currentTime = Date.now();
                const timeUntilExpiry = userInfo.expires_at - currentTime;
                const threeDaysInMs = 3 * 24 * 60 * 60 * 1000; // 3天的毫秒数
                
                // 只有在剩余时间少于3天时才延长，避免频繁更新
                if (timeUntilExpiry < threeDaysInMs) {
                    const updatedUserInfo = {
                        ...userInfo,
                        last_access_time: currentTime, // 记录最后访问时间
                        expires_at: currentTime + (7 * 24 * 60 * 60 * 1000) // 重新计算过期时间
                    };
                    localStorage.setItem('odoo_equipment_user', JSON.stringify(updatedUserInfo));
                    console.log('用户登录状态已延长7天，新的过期时间:', new Date(updatedUserInfo.expires_at));
                } else {
                    // 只更新最后访问时间，不延长过期时间
                    const updatedUserInfo = {
                        ...userInfo,
                        last_access_time: currentTime
                    };
                    localStorage.setItem('odoo_equipment_user', JSON.stringify(updatedUserInfo));
                    console.log('更新最后访问时间，过期时间无需延长 (剩余天数:', Math.ceil(timeUntilExpiry / (24 * 60 * 60 * 1000)), '天)');
                }
            }
        } catch (error) {
            console.error('延长用户信息过期时间失败:', error);
        }
    }
}
