# -*- coding: utf-8 -*-
{
    'name': "ylhc_price_list",

    'summary': """
        extend product pricelist
    """,

    'description': """
        extend product pricelist
    """,

    'author': "ylhctec",
    'website': "https://www.ylhctec.com",

    'category': 'apps/pricelist',
    'version': '********',
    'price': 9999.00,
    # 'post_init_hook': '_init_partner_private_pricelist',
    # 'uninstall_hook': '_uninstall_hook',

    'depends': ['base', 'web', 'sale', 'purchase', 'product', 'account', 'stock', 'ylhc_skip_save', 'ylch_base_tools', 'ylhc_prodcut_alias'],

    'data': [
        'security/ir.model.access.csv',
        'wizards/create_pricelist_wizard.xml',
        'wizards/global_pricelists_wizard.xml',
        'wizards/sale_order_history_price_views.xml',
        'wizards/sale_order_line_no_pricelist_views.xml',
        'wizards/open_global_pricelists_wizard_views.xml',
        'wizards/offsite_price_inquiry_wizard_views.xml',
        'views/ylhc_price_list.xml',
        'views/ylhc_product_item.xml',
        'views/ylhc_product.xml',
        'views/ylhc_partner.xml',
        'views/ylhc_sale.xml',
        'views/product_category_views.xml',
        'views/source_product_website_views.xml',
        'views/product_product_stock_inherit_tree.xml',
        'views/product_package_code_views.xml',
        'data/ir_actions_server.xml',
    ],

    'assets': {
        'web.assets_backend': [
            'ylhc_price_list/static/src/scss/price_unit_widget.scss',
            'ylhc_price_list/static/src/scss/disabled_buttons.scss',
            'ylhc_price_list/static/src/css/sale_order_buttons.css',
            'ylhc_price_list/static/src/js/price_unit_widget.js',
            'ylhc_price_list/static/src/xml/price_unit_widget.xml',
        ],
    },

    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
}
