.equipment-inspection-list-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  z-index: auto;

  .equipment-inspection-list-header {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #00A09D 0%, #00bcd4 100%);
    padding: 32px 16px 24px 16px;
    border-radius: 0 0 15px 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    color: #fff;
    position: relative;
    width: 100%;
    margin: 0;

    .back-button {
      position: absolute;
      left: 16px;
      top: 50%;
      transform: translateY(-50%);
      background: none;
      border: none;
      color: #fff;
      font-size: 1.2rem;
      cursor: pointer;
      padding: 8px;

      &:hover {
        opacity: 0.8;
      }
    }

    h1 {
      margin: 0 auto;
      font-size: 20px;
      font-weight: 600;
      color: #fff;
      line-height: 1;
    }

    .add-button {
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.4);
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 1.2rem;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-50%) scale(1.1);
      }

      &:active {
        transform: translateY(-50%) scale(0.95);
      }
    }
  }

  // 统计数据区域样式
  .inspection-stats {
    display: flex;
    justify-content: space-around;
    padding: 16px;
    background: #fff;
    margin: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .inspection-stat {
      flex: 1;
      text-align: center;
      padding: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      border-radius: 8px;

      &:hover {
        background: #f5f5f5;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &:active {
        transform: translateY(0);
      }

      .inspection-stat-number {
        font-size: 28px;
        font-weight: bold;
        color: #00A09D;
        margin-bottom: 4px;
      }

      .inspection-stat-label {
        font-size: 14px;
        color: #666;
        font-weight: 500;
      }
    }
  }

  .equipment-inspection-list-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .filter-section {
      background-color: #fff;
      border-bottom: 1px solid #e0e0e0;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      position: relative;
      z-index: 10;

      .filter-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        cursor: pointer;

        .filter-header-left {
          display: flex;
          align-items: center;
          gap: 10px;

          span {
            font-weight: 500;
            color: #333;
          }

          i {
            transition: transform 0.3s ease;
            color: #666;

            &.rotated {
              transform: rotate(180deg);
            }
          }
        }

        .preview-img-btn {
          background-color: #00A09D;
          color: white;
          border: none;
          padding: 8px 12px;
          border-radius: 6px;
          font-size: 14px;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 5px;

          &:hover {
            background-color: #008a87;
          }

          i {
            font-size: 14px;
          }
        }
      }

      .filter-content {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
        padding: 0 20px;

        &.expanded {
          max-height: 500px;
          padding: 0 20px 20px 20px;
        }

        .filter-row {
          display: flex;
          gap: 10px;
          margin-bottom: 15px;

          select, input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            background-color: #fff;

            &:focus {
              outline: none;
              border-color: #00A09D;
              box-shadow: 0 0 0 2px rgba(0, 160, 157, 0.2);
            }
          }

          .date-input {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;

            &::-webkit-calendar-picker-indicator {
              background: transparent;
              bottom: 0;
              color: transparent;
              cursor: pointer;
              height: auto;
              left: 0;
              position: absolute;
              right: 0;
              top: 0;
              width: auto;
            }
          }

        }

        .filter-btn-row {
          margin-top: 10px;
          margin-bottom: 0;

          .filter-btn-group {
            display: flex;
            gap: 10px;
            justify-content: center;

            .filter-submit-btn, .reset-btn {
              flex: 1;
              padding: 12px 20px;
              border: none;
              border-radius: 6px;
              font-size: 14px;
              font-weight: 500;
              cursor: pointer;
              transition: all 0.2s;

              &:active {
                transform: translateY(1px);
              }
            }

            .filter-submit-btn {
              background-color: #00A09D;
              color: white;

              &:hover {
                background-color: #008a87;
              }
            }

            .reset-btn {
              background-color: #f5f5f5;
              color: #666;
              border: 1px solid #ddd;

              &:hover {
                background-color: #e9e9e9;
                color: #333;
              }
            }
          }

        }

        .filter-item.user-filter {
          display: flex;
          align-items: center;
          position: relative;
          flex: 1;
          min-width: 0;

          .user-search-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            background: #fafbfc;
            transition: border 0.2s;
            box-sizing: border-box;

            &:focus {
              outline: none;
              border-color: #00A09D;
              box-shadow: 0 0 0 2px rgba(0, 160, 157, 0.2);
            }
          }
        }
      }
    }

    .inspection-list-section {
      display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;
      min-height: 0;
    }

    .inspection-list-scroll {
      flex: 1;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;

      .inspection-list-items {
        padding: 0;
      }
    }

    .inspection-list-item {
      background: #fff;
      border-bottom: 1px solid #e0e0e0;
      padding: 16px;
      display: flex;
      align-items: center;
      font-size: 15px;
      color: #333;
      transition: background-color 0.2s;

      &:active {
        background: #f5f5f5;
      }

      input[type="checkbox"] {
        margin-right: 12px;
        width: 18px;
        height: 18px;
        cursor: pointer;
      }


    }

    .inspection-list-name {
      font-weight: 500;
      color: #333;
      font-size: 16px;
      line-height: 1.3;
      margin-bottom: 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .inspection-list-description {
      color: #666;
      font-size: 13px;
      line-height: 1.4;
    }

    .inspection-arrow {
      color: #ccc;
      font-size: 16px;
      margin-left: 12px;
      align-self: center;
    }

    .inspection-list-loading {
      text-align: center;
      padding: 20px;
      color: #666;
      font-size: 14px;

      .fa-spinner {
        margin-right: 8px;
        color: #00A09D;
      }
    }

    .inspection-list-no-more {
      text-align: center;
      padding: 20px;
      color: #999;
      font-size: 13px;
    }

    .inspection-list-empty {
      text-align: center;
      padding: 40px 20px;
      color: #999;
      font-size: 14px;
    }
  }
}

// 用户选择弹窗
.filter-item.user-filter {
  .user-select-popup {
    position: absolute;
    z-index: 1000;
    top: 100%;
    left: 0;
    right: 0;
    margin-top: 2px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 0;

    .user-select-list {
      max-height: 220px;
      overflow-y: auto;

      .user-option {
        padding: 12px;
        cursor: pointer;
        border-bottom: 1px solid #f0f0f0;

        &:hover {
          background-color: #f8f9fa;
        }

        &:last-child {
          border-bottom: none;
        }
      }
    }
  }
}
