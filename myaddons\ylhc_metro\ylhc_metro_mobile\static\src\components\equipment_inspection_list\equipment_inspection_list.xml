<?xml version="1.0" encoding="UTF-8"?>
<templates>
  <t t-name="equipment_inspection_list.EquipmentInspectionList">
    <div class="equipment-inspection-list-container">
      <div class="equipment-inspection-list-header">
        <button class="back-button" t-on-click="goBack">
            <i><img src="/ylhc_metro_mobile/static/image/icons/go-back.png" style="width: 24px; height: 24px;" /></i>
        </button>
        <h1><t t-esc="state.dateStr"/></h1>
        <button class="add-button" t-on-click="addNewInspection">
            <i class="fa fa-plus"></i>
        </button>
      </div>
      
      <div class="equipment-inspection-list-content">
        <div class="filter-section">
          <div class="filter-header">
            <div class="filter-header-left" t-on-click="toggleFilter" t-att-class="{ 'rotated': state.isFilterExpanded }">
              <span>筛选条件</span>
              <i class="fa fa-chevron-down" t-att-class="{ 'rotated': state.isFilterExpanded }"></i>
            </div>
            <t t-if="state.selectedItems.length > 0">
              <button class="preview-img-btn" t-on-click="generateReport">
                <i class="fa fa-file-image-o"></i> 生成图片报告
              </button>
            </t>
          </div>
          <div class="filter-content" t-att-class="{ 'expanded': state.isFilterExpanded }">
            <div class="filter-row">
              <select t-model="state.filter_line" t-on-change="onLineChange">
                <option value="">线路</option>
                <t t-foreach="state.lineOptions" t-as="line" t-key="line[0]">
                  <option t-att-value="line[0]"><t t-esc="line[1]"/></option>
                </t>
              </select>
              <select t-model="state.filter_station" t-on-change="onFilterChange">
                <option value="">站点</option>
                <t t-foreach="state.stationOptions" t-as="station" t-key="station[0]">
                  <option t-att-value="station[0]"><t t-esc="station[1]"/></option>
                </t>
              </select>
            </div>
            <div class="filter-row">
              <select t-model="state.filter_major" t-on-change="onFilterChange">
                <option value="">专业</option>
                <t t-foreach="state.majorOptions" t-as="major" t-key="major[0]">
                  <option t-att-value="major[0]"><t t-esc="major[1]"/></option>
                </t>
              </select>
              <select t-model="state.filter_type" t-on-change="onFilterChange">
                <option value="">类型</option>
                <t t-foreach="state.typeOptions" t-as="type" t-key="type[0]">
                  <option t-att-value="type[0]"><t t-esc="type[1]"/></option>
                </t>
              </select>
            </div>
            <div class="filter-row">
              <select t-model="state.filter_status" t-on-change="onFilterChange">
                <option value="">状态</option>
                <option value="draft">草稿</option>
                <option value="in_progress">进行中</option>
                <option value="done">已完成</option>
                <option value="cancelled">已取消</option>
              </select>
              <input type="date"
                     t-model="state.filter_date"
                     t-on-change="onFilterChange"
                     placeholder="选择日期"
                     class="date-input"/>
            </div>
            <div class="filter-row">
              <div class="filter-item user-filter">
                <input type="text"
                       t-model="state.userSearchQuery"
                       t-on-input="onUserSearchInput"
                       t-on-focus="openUserSelect"
                       t-on-blur="handleUserInputBlur"
                       t-att-placeholder="state.filter_user_name ? state.filter_user_name : '请输入或选择检查人'"
                       autocomplete="off"
                       class="user-search-input"/>

                <t t-if="state.showUserSelect and state.filteredUserOptions.length">
                  <div class="user-select-popup">
                    <div class="user-select-list">
                      <t t-foreach="state.filteredUserOptions" t-as="user" t-key="user[0]">
                        <div class="user-option" t-on-click="() => selectUser(user[0], user[1])">
                          <t t-esc="user[1]"/>
                        </div>
                      </t>
                    </div>
                  </div>
                </t>
              </div>
            </div>
            <div class="filter-row filter-btn-row">
              <div class="filter-btn-group">
                <button class="filter-submit-btn" t-on-click="onFilter">筛选</button>
                <button t-on-click="onResetFilter" class="reset-btn">重置</button>
              </div>
            </div>
          </div>
        </div>

        <div class="inspection-list-section">
          <div class="inspection-list-scroll">
            <div class="inspection-list-items">
              <t t-foreach="state.inspectionData" t-as="inspection" t-key="inspection.id">
                <div class="inspection-list-item" t-on-click="() => viewDetail(inspection)">
                  <input type="checkbox"
                         t-att-checked="isSelected(inspection.id)"
                         t-on-click.stop="() => toggleSelection(inspection.id, !isSelected(inspection.id))"/>
                  <div style="flex:1;min-width:0;display:flex;flex-direction:column;">
                    <span class="inspection-list-name"><t t-esc="inspection.name"/></span>
                    <span class="inspection-list-description" style="margin-top:4px;">时间：<t t-esc="inspection.time"/></span>
                    <span class="inspection-list-description" style="margin-top:2px;">巡检人：<t t-esc="inspection.inspection_persons"/></span>
                    <span class="inspection-list-description" style="margin-top:2px;">专业：<t t-esc="inspection.major"/>  类型：<t t-esc="inspection.type"/></span>
                    <span class="inspection-list-description" style="margin-top:2px;"><t t-esc="inspection.location_description"/></span>
                  </div>
                  <i class="fa fa-chevron-right inspection-arrow"></i>
                </div>
              </t>

              <!-- 加载状态和提示 -->
              <t t-if="state.loading">
                <div class="inspection-list-loading">
                  <i class="fa fa-spinner fa-spin"></i> 加载中...
                </div>
              </t>

              <t t-if="!state.hasMore and !state.loading and state.inspectionData.length > 0">
                <div class="inspection-list-no-more">
                  - 没有更多数据了 -
                </div>
              </t>

              <t t-if="state.inspectionData.length === 0 and !state.loading">
                <div class="inspection-list-empty">
                  暂无巡检记录
                </div>
              </t>
            </div>
          </div>
        </div>
      </div>
    </div>
  </t>
</templates>
