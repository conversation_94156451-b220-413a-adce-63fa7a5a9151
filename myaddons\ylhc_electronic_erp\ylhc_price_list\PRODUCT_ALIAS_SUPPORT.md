# 产品别名支持功能

## 概述

本次修改为 `ylhc_price_list` 模块添加了产品别名支持功能，使得在进行站外价格查询时，不仅可以使用产品的主名称进行匹配，还可以使用产品的别名进行匹配。

## 修改内容

### 1. 依赖关系更新

在 `__manifest__.py` 中添加了对 `ylhc_prodcut_alias` 模块的依赖：

```python
'depends': ['base', 'web', 'sale', 'purchase', 'product', 'account', 'stock', 'ylhc_skip_save', 'ylch_base_tools', 'ylhc_prodcut_alias'],
```

### 2. 新增辅助方法

添加了 `_get_product_match_names()` 方法，用于获取产品的所有可能匹配名称：

```python
def _get_product_match_names(self):
    """
    获取产品的所有可能匹配名称（包括产品名称和所有别名）
    :return: set 包含产品名称和所有别名的集合
    """
    match_names = {self.name}  # 使用集合避免重复
    
    # 如果产品有别名，添加所有别名
    if hasattr(self, 'alias_ids') and self.alias_ids:
        for alias in self.alias_ids:
            if alias.name:
                match_names.add(alias.name)
    
    return match_names
```

### 3. 修改的方法

#### 3.1 `get_jlc_product_data()` 方法

**修改前：**
```python
records = [item for item in item_list if item["产品型号"] == self.name]
```

**修改后：**
```python
# 获取产品的所有可能匹配名称（包括产品名称和所有别名）
match_names = self._get_product_match_names()

# 使用产品名称和别名进行匹配，同时记录匹配的名称
records = []
for item in item_list:
    product_model = item["产品型号"]
    if product_model in match_names:
        # 添加匹配信息到记录中
        item_with_match = item.copy()
        item_with_match["_matched_name"] = product_model
        records.append(item_with_match)
```

**别名显示功能：**
```python
# 1.1 添加匹配别名信息
matched_name = record.get('_matched_name')
if matched_name and matched_name != self.name:
    # 如果匹配的名称不是产品主名称，说明是通过别名匹配的
    line_ids.append((0, 0, {
        'code': 'matched_alias',
        'key': '匹配别名',
        'value': matched_name,
        'type': 'product_description',
        'offsite_id': offsite.id
    }))
```

#### 3.2 `jbchip_product_price_inquiry()` 方法

**修改前：**
```python
records = [item for item in data.get('records', []) if item["goodsName"] == self.name]
```

**修改后：**
```python
# 获取产品的所有可能匹配名称（包括产品名称和所有别名）
match_names = self._get_product_match_names()

# 使用产品名称和别名进行匹配，同时记录匹配的名称
records = []
for item in data.get('records', []):
    goods_name = item["goodsName"]
    if goods_name in match_names:
        # 添加匹配信息到记录中
        item_with_match = item.copy()
        item_with_match["_matched_name"] = goods_name
        records.append(item_with_match)
```

**别名显示功能：**
```python
# 2.1 添加匹配别名信息
matched_name = record.get('_matched_name')
if matched_name and matched_name != self.name:
    # 如果匹配的名称不是产品主名称，说明是通过别名匹配的
    line_ids.append((0, 0, {
        'code': 'matched_alias',
        'key': '匹配别名',
        'value': matched_name,
        'type': 'product_description',
        'offsite_id': source_data.get('source_website_id')
    }))
```

### 4. 测试方法

添加了 `test_product_match_names()` 测试方法，用于调试和验证别名匹配功能：

```python
def test_product_match_names(self):
    """
    测试方法：获取产品的所有匹配名称
    用于调试和验证别名匹配功能
    """
    match_names = self._get_product_match_names()
    _logger.info(f"产品 {self.name} 的所有匹配名称: {match_names}")
    return {
        'type': 'ir.actions.client',
        'tag': 'display_notification',
        'params': {
            'title': '产品匹配名称',
            'message': f'产品 {self.name} 的所有匹配名称: {", ".join(match_names)}',
            'sticky': True,
            'type': 'info',
        }
    }
```

## 功能特点

1. **向后兼容**：如果产品没有别名，功能与之前完全相同
2. **多别名支持**：支持产品的多个别名同时匹配
3. **去重处理**：使用集合(set)避免重复的匹配名称
4. **性能优化**：只在需要时才获取别名信息
5. **别名显示**：当通过别名匹配时，会在结果中显示"匹配别名"信息
6. **智能识别**：只有当匹配的名称不是产品主名称时才显示别名信息

## 使用场景

当产品在不同供应商网站上使用不同的型号名称时，可以通过设置产品别名来实现匹配：

- 产品主名称：`STM32F103C8T6`
- 别名1：`STM32F103C8`
- 别名2：`STM32F103C8T6TR`

这样在进行站外价格查询时，无论供应商网站返回的是哪个名称，都能正确匹配到对应的产品。

## 注意事项

1. 确保 `ylhc_prodcut_alias` 模块已正确安装和配置
2. 产品别名应该准确设置，避免误匹配
3. 如果只需要匹配第一个结果，可以在匹配循环中添加 `break` 语句
